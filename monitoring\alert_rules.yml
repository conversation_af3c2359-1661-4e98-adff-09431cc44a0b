groups:
  - name: newsbalancer.rules
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"

      - alert: LLMAnalysisFailures
        expr: rate(newsbalancer_llm_analyses_total{status="failed"}[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "LLM analysis failures detected"
          description: "LLM analysis failure rate is {{ $value }} failures per second"

      - alert: LLMRateLimitExceeded
        expr: rate(newsbalancer_llm_errors_total{type="rate_limit"}[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "LLM rate limit exceeded"
          description: "LLM rate limit errors: {{ $value }} per second"

      - alert: DatabaseConnectionsHigh
        expr: newsbalancer_db_connections_active > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Active database connections: {{ $value }}"

      - alert: MemoryUsageHigh
        expr: (process_resident_memory_bytes / 1024 / 1024) > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }} MB"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute"

      - alert: LowConfidenceScores
        expr: avg(newsbalancer_llm_confidence_score) < 0.3
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low confidence scores detected"
          description: "Average confidence score is {{ $value }}"

      - alert: QueueBacklog
        expr: newsbalancer_llm_queue_size > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "LLM analysis queue backlog"
          description: "Queue size is {{ $value }} items"
