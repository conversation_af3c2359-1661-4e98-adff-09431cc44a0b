# Codecov configuration for NewsBalancer

coverage:
  precision: 2
  round: down
  range: "70...100"

  status:
    project:
      default:
        target: 80%
        threshold: 1%
        if_not_found: success
        if_ci_failed: error
    patch:
      default:
        target: 75%
        threshold: 2%
        if_not_found: success
        if_ci_failed: error

  ignore:
    - "cmd/"
    - "scripts/"
    - "docs/"
    - "postman/"
    - "monitoring/"
    - "**/*_test.go"
    - "**/testdata/"
    - "**/mock*.go"
    - "**/vendor/"

comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes

github_checks:
  annotations: true
