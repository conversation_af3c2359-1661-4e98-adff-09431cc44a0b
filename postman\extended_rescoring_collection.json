{"info": {"name": "Extended Rescoring Collection", "description": "Extended test suite for advanced rescoring functionality, including edge cases, performance testing, and comprehensive LLM analysis workflows. This collection tests complex rescoring scenarios, batch operations, and error recovery mechanisms.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.globals.set('articleSchema', {", "    type: 'object',", "    required: ['success', 'data'],", "    properties: {", "        success: { type: 'boolean' },", "        data: {", "            type: 'object',", "            required: ['article_id', 'title', 'content', 'url', 'source'],", "            properties: {", "                article_id: { type: 'number' },", "                title: { type: 'string' },", "                content: { type: 'string' },", "                url: { type: 'string' },", "                source: { type: 'string' },", "                composite_score: { type: ['number', 'null'] },", "                confidence: { type: ['number', 'null'] }", "            }", "        }", "    }", "});", "", "pm.globals.set('errorSchema', {", "    type: 'object',", "    required: ['success', 'error'],", "    properties: {", "        success: { type: 'boolean' },", "        error: {", "            type: 'object',", "            required: ['code', 'message'],", "            properties: {", "                code: { type: 'string' },", "                message: { type: 'string' }", "            }", "        }", "    }", "});"]}}], "item": [{"name": "1. Extended Rescoring Tests", "item": [{"name": "1.1 Setup Test Articles", "item": [{"name": "Create Test Article for Rescoring", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Extended Rescoring Test Article\",\n  \"content\": \"This article is specifically designed to test extended rescoring functionality. It contains political content that should trigger bias analysis across multiple perspectives. The content discusses various political viewpoints and policy positions to ensure comprehensive LLM analysis.\",\n  \"source\": \"test-extended\",\n  \"url\": \"https://example.com/extended-rescoring-{{$timestamp}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "", "pm.test(\"Response matches article schema\", function () {", "    const schema = pm.globals.get('articleSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    var json = pm.response.json();", "    pm.environment.set(\"extendedArticleId\", json.data.article_id);", "    console.log('Created extended test article with ID:', json.data.article_id);", "}"], "type": "text/javascript"}}]}]}, {"name": "1.2 Basic Rescoring Operations", "item": [{"name": "Trigger Initial Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{extendedArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{extendedArticleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202 (Accepted)\", function () {", "    pm.response.to.have.status(202);", "});", "", "pm.test(\"Response indicates analysis started\", function () {", "    var json = pm.response.json();", "    pm.expect(json.success).to.be.true;", "    pm.expect(json.message).to.include('started');", "});", "", "// Wait a moment for analysis to begin", "setTimeout(function(){}, 2000);"], "type": "text/javascript"}}]}, {"name": "Monitor Analysis Progress", "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/event-stream"}], "url": {"raw": "{{baseUrl}}/api/llm/score-progress/{{extendedArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "score-progress", "{{extendedArticleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is SSE format\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('text/event-stream');", "});", "", "pm.test(\"Response contains progress data\", function () {", "    var responseText = pm.response.text();", "    pm.expect(responseText).to.include('data:');", "});"], "type": "text/javascript"}}]}, {"name": "Verify Rescoring Results", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles/{{extendedArticleId}}/bias", "host": ["{{baseUrl}}"], "path": ["api", "articles", "{{extendedArticleId}}", "bias"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains bias analysis\", function () {", "    var json = pm.response.json();", "    pm.expect(json.success).to.be.true;", "    pm.expect(json.data).to.have.property('composite_score');", "    pm.expect(json.data).to.have.property('results');", "    pm.expect(json.data.results).to.be.an('array');", "});", "", "pm.test(\"Composite score is within valid range\", function () {", "    var json = pm.response.json();", "    if (json.data.composite_score !== null) {", "        pm.expect(json.data.composite_score).to.be.at.least(-1);", "        pm.expect(json.data.composite_score).to.be.at.most(1);", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "1.3 Advanced Rescoring Scenarios", "item": [{"name": "Multiple Consecutive Rescoring", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{extendedArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{extendedArticleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202 for consecutive rescoring\", function () {", "    pm.response.to.have.status(202);", "});", "", "pm.test(\"System handles multiple rescoring requests\", function () {", "    var json = pm.response.json();", "    pm.expect(json.success).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "Rescoring Non-existent Article", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/99999999", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "99999999"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404 for non-existent article\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Response contains appropriate error message\", function () {", "    var json = pm.response.json();", "    pm.expect(json.success).to.be.false;", "    pm.expect(json.error.message).to.include('not found');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "2. Performance and Load Testing", "item": [{"name": "2.1 Batch Operations", "item": [{"name": "Get Feed Health Status", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/feeds/healthz", "host": ["{{baseUrl}}"], "path": ["api", "feeds", "healthz"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains feed health data\", function () {", "    var json = pm.response.json();", "    pm.expect(json).to.be.an('object');", "    // Each feed should have a boolean health status", "    Object.values(json).forEach(status => {", "        pm.expect(status).to.be.a('boolean');", "    });", "});"], "type": "text/javascript"}}]}]}]}]}