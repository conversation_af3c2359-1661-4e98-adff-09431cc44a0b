mode: set
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:15.60,16.18 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:16.18,18.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:21.2,25.22 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:25.22,27.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:29.2,29.14 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:50.68,62.27 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:62.27,64.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:67.2,69.19 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:69.19,71.41 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:71.41,80.4 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:80.9,85.81 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:85.81,87.44 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:87.44,89.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:89.11,91.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:96.2,106.3 4 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:113.54,114.25 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:114.25,116.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:120.51,121.25 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:121.25,123.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:127.72,128.25 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:128.25,131.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:135.51,136.25 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:136.25,138.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:150.34,152.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:161.39,163.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:166.63,167.48 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:167.48,169.10 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:169.10,173.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:174.3,174.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:174.30,176.4 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:178.3,178.22 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:180.2,180.19 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:184.62,190.2 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:193.54,195.34 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:195.34,196.12 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:196.12,198.4 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:199.3,199.33 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/client.go:201.2,201.12 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:12.96,17.51 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:17.51,18.45 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:18.45,20.4 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:23.2,25.59 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:25.59,26.18 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:26.18,29.4 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:31.3,39.17 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:39.17,41.12 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:45.3,47.8 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:50.2,50.20 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:50.20,52.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:55.2,56.22 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:60.81,64.51 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:64.51,65.43 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:65.43,67.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:71.2,74.59 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:74.59,75.18 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:75.18,78.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:79.3,80.17 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:80.17,82.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:86.3,88.8 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:91.2,91.20 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:91.20,93.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:96.2,97.21 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:101.86,105.51 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:105.51,106.41 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:106.41,108.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:112.2,115.59 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:115.59,116.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:116.18,119.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:120.3,121.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:121.17,123.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:126.3,128.8 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:131.2,131.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:131.20,133.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:136.2,137.21 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:141.91,145.51 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:145.51,146.46 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:146.46,148.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:152.2,155.59 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:155.59,156.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:156.18,159.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:160.3,161.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:161.17,163.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:167.3,169.8 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:172.2,172.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:172.20,174.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:177.2,178.18 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:182.92,186.51 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:186.51,188.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:191.2,194.59 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:194.59,195.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:195.18,198.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:199.3,200.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:200.17,202.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:205.3,207.8 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:210.2,210.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:210.20,212.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:215.2,216.22 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:220.114,230.59 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:230.59,231.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:231.18,234.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:235.3,236.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:236.17,238.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:242.3,246.19 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:249.2,249.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:253.110,255.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:255.16,261.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:263.2,264.59 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:264.59,265.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:265.18,268.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:269.3,270.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:270.17,272.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:276.3,277.21 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:280.2,280.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:284.76,288.51 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:288.51,289.44 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:289.44,291.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:295.2,298.59 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:298.59,299.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:299.18,302.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:303.3,304.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:304.17,306.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:309.3,311.8 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:314.2,314.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:314.20,316.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:319.2,323.20 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:327.73,329.59 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:329.59,330.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:330.18,333.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:334.3,335.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:335.17,337.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:340.3,340.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:343.2,343.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:347.61,355.50 5 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:355.50,357.10 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:357.10,359.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:360.3,360.51 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:360.51,362.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:363.3,363.14 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:368.60,377.50 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:377.50,379.64 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:379.64,381.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:382.3,382.14 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:385.2,387.14 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/methods.go:391.34,393.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:70.65,72.34 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:72.34,74.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:75.2,75.17 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:79.54,80.16 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:80.16,82.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:83.2,94.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:98.72,99.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:99.16,101.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:103.2,104.40 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:104.40,111.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:113.2,117.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:121.53,122.16 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:122.16,124.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:128.2,128.57 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:128.57,135.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:144.2,145.106 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:145.106,153.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:159.2,161.97 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:161.97,168.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:171.2,171.59 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:171.59,178.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:180.2,180.50 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:180.50,187.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:190.2,190.39 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:190.39,192.21 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:192.21,194.73 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:194.73,201.5 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:206.2,211.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:215.43,216.31 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:217.40,218.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:219.60,220.31 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:221.46,222.33 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:223.36,224.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:225.41,226.36 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:227.44,228.39 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:229.17,230.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:231.35,232.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:233.10,234.40 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:239.52,240.20 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:241.29,242.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:243.31,244.24 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:245.28,246.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:247.27,248.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:249.27,250.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:251.33,252.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:253.34,254.22 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:255.38,256.26 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:257.29,258.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:259.37,260.31 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:261.33,262.27 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:263.10,264.25 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:269.55,270.20 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:271.29,272.48 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:273.31,274.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:275.28,276.25 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:277.27,278.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:279.27,280.56 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:281.33,282.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:283.34,284.51 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:285.38,286.33 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:287.29,288.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:289.37,290.43 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:291.33,292.27 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/wrapper/models.go:293.10,294.50 1 0
