/* Utility Classes */
/* Small utility classes for common styling needs */

/* Loading and display utilities */
.loading-hidden {
  display: none;
  margin-left: 10px;
}



.filter-info {
  margin-left: 10px;
}

/* Progress and button loading states */
.progress-hidden {
  display: none;
  margin: 15px 0;
}

.btn-loading-hidden {
  display: none;
}

/* Common utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Margin utilities */
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-4 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-5 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

/* Padding utilities */
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Display utilities */
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

/* Flexbox utilities */
.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}



/* Load More Section */
.load-more-section {
  margin: var(--space-5, 1.25rem) 0;
  text-align: center;
}

/* Button Spacing */
.btn-group .btn {
  margin-right: var(--space-2-5, 0.625rem);
  margin-bottom: var(--space-2-5, 0.625rem);
}

.btn-group .btn:last-child {
  margin-right: 0;
}
